# Fix for Re-invitation Issue

## Problem Summary
When an admin removes a user from a project and later tries to re-invite the same user (same email), the system fails to create the new invitation due to a database unique constraint violation. The error occurs because:

1. When a user is removed, their `project_users` record is soft-deleted (`is_active = false`)
2. The invitation API only checks for active memberships (`is_active = true`)
3. When trying to re-invite, it attempts to create a new record
4. The database has a UNIQUE constraint on `(project_id, user_id, role)` which prevents duplicate records

## Solution Implemented

### 1. Modified Invitation Logic (`/api/projects/invite-user`)
- **Before**: Only checked for active memberships (`is_active = true`)
- **After**: Checks for ANY existing membership (active or inactive) for the specific project and role

### 2. Added Re-activation Logic
- **Inactive Membership**: Reactivates the existing record instead of creating a new one
- **Declined Invitation**: Updates status from 'declined' to 'invited'
- **New User**: Creates a new record as before

### 3. Enhanced Error Handling
- Added input validation for role and email format
- Improved error messages for database constraint violations
- Added comprehensive logging for debugging

## Code Changes

### File: `src/app/api/projects/invite-user/route.ts`

#### Key Changes:
1. **Modified membership check query**:
   ```typescript
   // OLD: Only checked active memberships
   .eq('is_active', true)
   
   // NEW: Checks for any membership with specific role
   .eq('role', role) // Added role-specific check
   // Removed is_active filter
   ```

2. **Added reactivation logic**:
   ```typescript
   if (existingMembership.is_active) {
     // Handle active memberships (accepted, invited, declined)
   } else {
     // Reactivate inactive membership
     await supabase.from('project_users').update({
       status: 'invited',
       is_active: true,
       deleted_at: null,
       deleted_by: null,
       // ... other fields
     })
   }
   ```

3. **Enhanced validation**:
   - Role validation against valid enum values
   - Email format validation
   - Better error messages

## Testing Instructions

### Manual Testing Steps:

1. **Test Normal Invitation** (should work as before):
   ```
   1. Login as admin
   2. Go to Members page
   3. Invite a new user who has never been in the project
   4. Verify invitation appears in the list
   ```

2. **Test Re-invitation After Removal** (main fix):
   ```
   1. Login as admin
   2. Add a user to the project
   3. Remove the user from the project
   4. Try to re-invite the same user with the same role
   5. Verify: No error occurs and invitation is created successfully
   ```

3. **Test Re-invitation After Declining**:
   ```
   1. Invite a user
   2. User declines the invitation
   3. Try to re-invite the same user
   4. Verify: Invitation is updated to 'invited' status
   ```

4. **Test Error Cases**:
   ```
   1. Try to invite user who is already an active member
   2. Try to invite user who already has pending invitation
   3. Verify appropriate error messages are shown
   ```

### Database Verification:

Check the `project_users` table to verify:
- Removed users have `is_active = false` and `deleted_at` timestamp
- Re-invited users have `is_active = true` and `deleted_at = null`
- No duplicate records are created

## Expected Behavior After Fix

### Scenario 1: Re-invite Removed User
- **Before**: Error (constraint violation) or silent failure
- **After**: Success - existing record reactivated with 'invited' status

### Scenario 2: Re-invite Declined User  
- **Before**: May work or fail depending on implementation
- **After**: Success - status updated from 'declined' to 'invited'

### Scenario 3: Normal New Invitation
- **Before**: Works
- **After**: Still works (no change)

## API Response Examples

### Success Response:
```json
{
  "success": true,
  "message": "User invited successfully",
  "invitation": {
    "id": "uuid",
    "project_id": "uuid", 
    "user_id": "uuid",
    "role": "technician",
    "status": "invited",
    "is_active": true
  }
}
```

### Error Responses:
```json
// Already active member
{
  "error": "User is already a member of this project"
}

// Pending invitation exists
{
  "error": "User already has a pending invitation to this project"
}

// Invalid input
{
  "error": "Invalid role. Must be one of: technician, competent_person, admin, viewer"
}
```

## Files Modified
- `src/app/api/projects/invite-user/route.ts` - Main fix implementation

## Files Created
- `test-invitation-scenarios.md` - Test scenarios documentation
- `SOLUTION_SUMMARY.md` - This summary document
