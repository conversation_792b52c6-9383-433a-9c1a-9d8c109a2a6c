'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useCompetentPersonsSearch,
  useCompetentPersonsStats,
} from '@/features/competent-persons';
import { useAdminProjectsWithCPs } from '@/features/competent-persons/hooks/use-competent-persons-search';
import { usePermissions } from '@/hooks/use-permissions';
import type { CompetentPerson } from '@/types/competent-person';
import { format } from 'date-fns';
import {
  Award,
  Building,
  Calendar,
  Edit,
  FileText,
  MapPin,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  UserCheck,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';
import { AddCPModal } from './components/add-cp-modal';
import { CertificatePreviewModal } from './components/certificate-preview-modal';

/**
 * Utility functions for formatting data
 */
function useCompetentPersonUtils() {
  const t = useTranslations('cpList.fields');

  const formatDate = (dateString?: string) => {
    if (!dateString) return t('notSpecified');
    try {
      return format(new Date(dateString), 'dd/MM/yy');
    } catch {
      return t('invalidDate');
    }
  };

  const getCPTypeBadgeVariant = (cpType: string) => {
    switch (cpType) {
      case 'CP1':
        return 'default';
      case 'CP2':
        return 'secondary';
      case 'CP3':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const isExpired = (certExpDate?: string) => {
    return certExpDate ? new Date(certExpDate) < new Date() : false;
  };

  return { formatDate, getCPTypeBadgeVariant, isExpired };
}

/**
 * Summary Statistics Component
 */
function SummaryCards() {
  const t = useTranslations('cpList');
  const { data: competentPersons = [], isLoading } = useCompetentPersonsStats();

  const stats = useMemo(() => {
    const total = competentPersons.length;
    const byType = competentPersons.reduce(
      (acc, person) => {
        acc[person.cp_type] = (acc[person.cp_type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const expired = competentPersons.filter((person) =>
      person.cert_exp_date
        ? new Date(person.cert_exp_date) < new Date()
        : false,
    ).length;

    const totalPMAs = competentPersons.reduce(
      (acc, person) => acc + person.no_of_pma,
      0,
    );

    return { total, byType, expired, totalPMAs };
  }, [competentPersons]);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('totalCPs')}</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('byType')}</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-1">
            {['CP1', 'CP2', 'CP3'].map((type) => (
              <div key={type} className="flex justify-between text-sm">
                <span>{type}:</span>
                <span className="font-medium">{stats.byType[type] || 0}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('expiredCerts')}
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-destructive">
            {stats.expired}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('totalPMAs')}
          </CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalPMAs}</div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Main CP List Page Component
 */
export default function CPListPage() {
  const t = useTranslations();
  const tFields = useTranslations('cpList.fields');
  const { isContractor, isJKR: isAdmin } = usePermissions();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingPerson, setEditingPerson] = useState<CompetentPerson | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [stateFilter, setStateFilter] = useState('');
  const [cpTypeFilter, setCpTypeFilter] = useState('');
  const [previewCertificate, setPreviewCertificate] = useState<{
    url: string;
    personName: string;
  } | null>(null);
  const { formatDate, getCPTypeBadgeVariant, isExpired } =
    useCompetentPersonUtils();

  // Contractor data fetching
  const {
    data: contractorSearchResult,
    isLoading: isContractorLoading,
    error: contractorError,
    isFetching: isContractorFetching,
  } = useCompetentPersonsSearch(searchQuery);

  // Admin data fetching
  const {
    data: adminSearchResult,
    isLoading: isAdminLoading,
    error: adminError,
    isFetching: isAdminFetching,
  } = useAdminProjectsWithCPs({
    searchQuery,
    stateFilter: stateFilter || undefined,
    cpTypeFilter: cpTypeFilter || undefined,
  });

  // Determine which data to use based on user role
  const isLoading = isContractor ? isContractorLoading : isAdminLoading;
  const error = isContractor ? contractorError : adminError;
  const isFetching = isContractor ? isContractorFetching : isAdminFetching;

  // Extract data based on user role
  const competentPersons = isContractor ? (contractorSearchResult?.data || []) : [];
  const totalCount = isContractor ? (contractorSearchResult?.count || 0) : 0;
  const projectsWithCPs = isAdmin ? (adminSearchResult?.data || []) : [];
  const totalProjects = isAdmin ? (adminSearchResult?.totalProjects || 0) : 0;
  const totalCPs = isAdmin ? (adminSearchResult?.totalCPs || 0) : 0;

  const hasCompetentPersons = competentPersons && competentPersons.length > 0;
  const hasProjectsWithCPs = projectsWithCPs && projectsWithCPs.length > 0;

  // Only show to contractors and admins
  if (!isContractor && !isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('cpList.accessRestricted.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('cpList.accessRestricted.description')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-8 bg-muted rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-6 bg-muted rounded w-3/4"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded w-5/6"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('cpList.errorLoading.title')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('cpList.errorLoading.description')}: {error.message}
            </p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render admin view
  if (isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Admin Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">{t('cpList.title')} </h1>
            <p className="text-muted-foreground mt-2">
              {t('cpList.admin.description')}
            </p>
          </div>
        </div>

        {/* Admin Filters */}
        <div className="flex gap-4 items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder={t('cpList.admin.searchPlaceholder')}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {isFetching && (
              <div className="absolute right-2.5 top-2.5">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              </div>
            )}
          </div>

          {/* State Filter */}
          <select
            value={stateFilter}
            onChange={(e) => setStateFilter(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="">{t('cpList.admin.allStates')}</option>
            <option value="JH">Johor</option>
            <option value="KD">Kedah</option>
            <option value="KT">Kelantan</option>
            <option value="ML">Melaka</option>
            <option value="NS">Negeri Sembilan</option>
            <option value="PH">Pahang</option>
            <option value="PN">Penang</option>
            <option value="PK">Perak</option>
            <option value="PL">Perlis</option>
            <option value="SB">Sabah</option>
            <option value="SW">Sarawak</option>
            <option value="SL">Selangor</option>
            <option value="TR">Terengganu</option>
            <option value="WP">Wilayah Persekutuan</option>
          </select>

          {/* CP Type Filter */}
          <select
            value={cpTypeFilter}
            onChange={(e) => setCpTypeFilter(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="">{t('cpList.admin.allCpTypes')}</option>
            <option value="CP1">CP1</option>
            <option value="CP2">CP2</option>
            <option value="CP3">CP3</option>
          </select>
        </div>

        {/* Admin Summary Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('cpList.admin.totalProjects')}</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalProjects}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('cpList.totalCPs')}</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCPs}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('cpList.admin.projectsWithCps')}</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projectsWithCPs.filter((p: any) => p.cp_stats.total > 0).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Projects with CPs List */}
        {hasProjectsWithCPs ? (
          <div className="space-y-6">
            {projectsWithCPs.map((project: any) => (
              <Card key={project.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Building className="h-5 w-5" />
                        {project.name}
                        {project.code && (
                          <Badge variant="outline">{project.code}</Badge>
                        )}
                      </CardTitle>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
                        <span className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {project.location}
                        </span>
                        <span>{t('cpList.admin.contractorLabel')}: {project.contractor_name}</span>
                        <span>{t('cpList.admin.stateLabel')}: {project.state}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant="secondary">
                        {project.cp_stats.total} {t('cpList.admin.cpsLabel')}
                      </Badge>
                      {project.cp_stats.expired > 0 && (
                        <Badge variant="destructive">
                          {project.cp_stats.expired} {t('cpList.admin.expiredLabel')}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>

                {project.competent_persons.length > 0 && (
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('cpList.admin.tableHeaders.name')}</TableHead>
                          <TableHead>{t('cpList.admin.tableHeaders.icNumber')}</TableHead>
                          <TableHead>{t('cpList.admin.tableHeaders.cpType')}</TableHead>
                          <TableHead>{t('cpList.admin.tableHeaders.phone')}</TableHead>
                          <TableHead>{t('cpList.admin.tableHeaders.certExpiry')}</TableHead>
                          <TableHead>{t('cpList.admin.tableHeaders.actions')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {project.competent_persons.map((person: any) => (
                          <TableRow key={person.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0 p-1.5 bg-primary/10 rounded-md">
                                  <UserCheck className="h-4 w-4 text-primary" />
                                </div>
                                <span>{person.name}</span>
                              </div>
                            </TableCell>
                            <TableCell className="font-mono text-sm">
                              {person.ic_no}
                            </TableCell>
                            <TableCell>
                              <Badge variant={getCPTypeBadgeVariant(person.cp_type)}>
                                {person.cp_type}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {person.phone_no ? (
                                <div className="flex items-center space-x-2">
                                  <Phone className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-sm">{person.phone_no}</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground text-sm">
                                  {tFields('notSpecified')}
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              {person.cert_exp_date ? (
                                <div className="flex items-center space-x-2">
                                  <Calendar className="h-3 w-3 text-muted-foreground" />
                                  <span
                                    className={`text-sm ${
                                      isExpired(person.cert_exp_date)
                                        ? 'text-destructive font-medium'
                                        : ''
                                    }`}
                                  >
                                    {formatDate(person.cert_exp_date)}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground text-sm">
                                  {tFields('notSpecified')}
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              {person.cp_registeration_cert && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    setPreviewCertificate({
                                      url: person.cp_registeration_cert!,
                                      personName: person.name,
                                    })
                                  }
                                  title={tFields('viewCert')}
                                >
                                  <FileText className="h-4 w-4" />
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Building className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold mb-2">
                {t('cpList.admin.noProjectsFound.title')}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {t('cpList.admin.noProjectsFound.description')}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Certificate Preview Modal */}
        {previewCertificate && (
          <CertificatePreviewModal
            open={!!previewCertificate}
            onOpenChange={(open) => {
              if (!open) {
                setPreviewCertificate(null);
              }
            }}
            certificateUrl={previewCertificate.url}
            personName={previewCertificate.personName}
          />
        )}
      </div>
    );
  }

  // Render contractor view
  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{t('cpList.title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('cpList.description')}
          </p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t('cpList.addCP')}
        </Button>
      </div>

      {hasCompetentPersons ? (
        <>
          {/* Summary Statistics */}
          <SummaryCards />

          {/* Competent Persons Table */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                Competent Persons ({competentPersons.length}/{totalCount})
              </h2>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search competent persons..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {isFetching && (
                  <div className="absolute right-2.5 top-2.5">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                  </div>
                )}
              </div>
            </div>
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{tFields('name')}</TableHead>
                    <TableHead>{tFields('icNo')}</TableHead>
                    <TableHead>{tFields('cpType')}</TableHead>
                    <TableHead>{tFields('phone')}</TableHead>
                    <TableHead>{tFields('registrationNo')}</TableHead>
                    <TableHead>{tFields('certExpiry')}</TableHead>
                    <TableHead className="text-center">
                      {tFields('pmas')}
                    </TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {competentPersons.map((person) => (
                    <TableRow key={person.id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 p-1.5 bg-primary/10 rounded-md">
                            <UserCheck className="h-4 w-4 text-primary" />
                          </div>
                          <span>{person.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {person.ic_no}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getCPTypeBadgeVariant(person.cp_type)}>
                          {person.cp_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {person.phone_no ? (
                          <div className="flex items-center space-x-2">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{person.phone_no}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {person.cp_registeration_no ? (
                          <div className="flex items-center space-x-2">
                            <Award className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">
                              {person.cp_registeration_no}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {person.cert_exp_date ? (
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span
                              className={`text-sm ${
                                isExpired(person.cert_exp_date)
                                  ? 'text-destructive font-medium'
                                  : ''
                              }`}
                            >
                              {formatDate(person.cert_exp_date)}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <FileText className="h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">
                            {person.no_of_pma}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {person.cp_registeration_cert && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                setPreviewCertificate({
                                  url: person.cp_registeration_cert!,
                                  personName: person.name,
                                })
                              }
                              title={tFields('viewCert')}
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => setEditingPerson(person)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {tFields('editAction')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </div>
        </>
      ) : (
        // Empty state
        <Card>
          <CardContent className="text-center py-12">
            <UserCheck className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {t('cpList.noCompetentPersons.title')}
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              {t('cpList.noCompetentPersons.description')}
            </p>
            <Button onClick={() => setIsAddModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              {t('cpList.addFirstCP')}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Add/Edit CP Modal - Only for contractors */}
      {isContractor && (
        <AddCPModal
          open={isAddModalOpen || !!editingPerson}
          onOpenChange={(open) => {
            if (!open) {
              setIsAddModalOpen(false);
              setEditingPerson(null);
            }
          }}
          editingPerson={editingPerson}
        />
      )}

      {/* Certificate Preview Modal */}
      {previewCertificate && (
        <CertificatePreviewModal
          open={!!previewCertificate}
          onOpenChange={(open) => {
            if (!open) {
              setPreviewCertificate(null);
            }
          }}
          certificateUrl={previewCertificate.url}
          personName={previewCertificate.personName}
        />
      )}
    </div>
  );
}
