# Test Scenarios for Re-invitation Fix

## Scenarios to Test

### 1. Normal Invitation (New User)
- **Setup**: User has never been invited to the project
- **Expected**: New project_users record created with status 'invited'
- **Test**: Invite a completely new user

### 2. Re-invitation After Removal
- **Setup**: User was previously a member, then removed (is_active = false)
- **Expected**: Existing record reactivated with status 'invited', is_active = true
- **Test**: 
  1. Add user to project
  2. Remove user from project
  3. Re-invite user

### 3. Re-invitation After Declining
- **Setup**: User previously declined invitation (status = 'declined', is_active = true)
- **Expected**: Existing record updated with status 'invited'
- **Test**:
  1. Invite user
  2. User declines invitation
  3. Re-invite user

### 4. Duplicate Active Invitation
- **Setup**: User already has pending invitation (status = 'invited', is_active = true)
- **Expected**: Error 409 - "User already has a pending invitation"
- **Test**: Try to invite user who already has pending invitation

### 5. Already Active Member
- **Setup**: User is already an active member (status = 'accepted', is_active = true)
- **Expected**: Error 409 - "User is already a member"
- **Test**: Try to invite user who is already a member

### 6. Different Roles
- **Setup**: User has one role, invite for different role
- **Expected**: New invitation for the different role
- **Test**: 
  1. User is 'technician'
  2. Invite same user as 'admin'

## Database States

### Before Fix (Problem)
```sql
-- User removed from project
UPDATE project_users SET is_active = false, deleted_at = NOW() WHERE id = 'user-id';

-- Try to re-invite (would fail due to unique constraint)
INSERT INTO project_users (project_id, user_id, role, status) 
VALUES ('proj-id', 'user-id', 'technician', 'invited');
-- ERROR: duplicate key value violates unique constraint
```

### After Fix (Solution)
```sql
-- Check for existing membership (any status, any is_active)
SELECT id, status, is_active, role FROM project_users 
WHERE project_id = 'proj-id' AND user_id = 'user-id' AND role = 'technician';

-- If inactive membership found, reactivate it
UPDATE project_users SET 
  status = 'invited', 
  is_active = true, 
  deleted_at = null, 
  deleted_by = null,
  updated_at = NOW()
WHERE id = 'existing-membership-id';
```

## API Response Examples

### Success Cases
```json
{
  "success": true,
  "message": "User invited successfully",
  "invitation": {
    "id": "uuid",
    "project_id": "uuid",
    "user_id": "uuid",
    "role": "technician",
    "status": "invited",
    "is_active": true
  }
}
```

### Error Cases
```json
{
  "error": "User is already a member of this project"
}

{
  "error": "User already has a pending invitation to this project"
}

{
  "error": "Failed to reactivate user membership"
}
```
