import { createServerSupabaseClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { projectId, userEmail, role } = await request.json();

    console.log('Invite user request:', { projectId, userEmail, role });

    if (!projectId || !userEmail || !role) {
      return NextResponse.json(
        { error: 'Missing required fields: projectId, userEmail, role' },
        { status: 400 },
      );
    }

    // Validate role
    const validRoles = ['technician', 'competent_person', 'admin', 'viewer'];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: `Invalid role. Must be one of: ${validRoles.join(', ')}` },
        { status: 400 },
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 },
      );
    }

    // Create server Supabase client with access to cookies
    const supabase = await createServerSupabaseClient();

    // Get current user from session with automatic refresh
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();
    let currentSessionData = sessionData;

    // If session is invalid or expired, try to refresh
    if (sessionError || !currentSessionData.session?.user) {
      const { data: refreshData, error: refreshError } =
        await supabase.auth.refreshSession();

      if (refreshError || !refreshData.session?.user) {
        console.error('Session validation failed:', {
          sessionError,
          refreshError,
        });
        return NextResponse.json(
          { error: 'Unauthorized: No valid session' },
          { status: 401 },
        );
      }

      currentSessionData = refreshData;
    }

    const currentUserId = currentSessionData.session!.user.id;

    // Check if current user has permission to invite (admin or owner of project)
    const { data: currentUserProject, error: permissionError } = await supabase
      .from('project_users')
      .select('role')
      .eq('project_id', projectId)
      .eq('user_id', currentUserId)
      .eq('status', 'accepted')
      .eq('is_active', true)
      .single();

    if (
      permissionError ||
      !currentUserProject ||
      currentUserProject.role !== 'admin'
    ) {
      return NextResponse.json(
        {
          error:
            'Unauthorized: You do not have permission to invite users to this project',
        },
        { status: 403 },
      );
    }

    // Check if user exists in the system
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single();

    if (userError && userError.code !== 'PGRST116') {
      // PGRST116 is "not found" error
      console.error('Error checking user:', userError);
      return NextResponse.json(
        { error: 'Database error while checking user' },
        { status: 500 },
      );
    }

    if (!existingUser) {
      return NextResponse.json(
        {
          error: 'User not found',
          message:
            'Only existing users can be invited to projects. The user must register first.',
        },
        { status: 404 },
      );
    }

    // Check if user has any existing membership (active or inactive) for this project and role
    const { data: existingMembership, error: membershipError } = await supabase
      .from('project_users')
      .select('id, status, is_active, role')
      .eq('project_id', projectId)
      .eq('user_id', existingUser.id)
      .eq('role', role)
      .single();

    if (membershipError && membershipError.code !== 'PGRST116') {
      console.error('Error checking membership:', membershipError);
      return NextResponse.json(
        { error: 'Database error while checking membership' },
        { status: 500 },
      );
    }

    let invitation;

    if (existingMembership) {
      console.log('Found existing membership:', existingMembership);
      // User has an existing membership record for this project and role
      if (existingMembership.is_active) {
        // Active membership - check status
        if (existingMembership.status === 'accepted') {
          return NextResponse.json(
            { error: 'User is already a member of this project' },
            { status: 409 },
          );
        } else if (existingMembership.status === 'invited') {
          return NextResponse.json(
            { error: 'User already has a pending invitation to this project' },
            { status: 409 },
          );
        } else if (existingMembership.status === 'declined') {
          // User previously declined - update to invited status
          const { data: updatedMembership, error: updateError } = await supabase
            .from('project_users')
            .update({
              status: 'invited',
              assigned_date: new Date().toISOString().split('T')[0],
              updated_at: new Date().toISOString(),
              updated_by: currentUserId,
            })
            .eq('id', existingMembership.id)
            .select()
            .single();

          if (updateError) {
            console.error('Error updating declined membership:', updateError);
            return NextResponse.json(
              { error: 'Failed to update user invitation' },
              { status: 500 },
            );
          }

          invitation = updatedMembership;
        }
      } else {
        // Inactive membership (previously removed) - reactivate it
        console.log('Reactivating inactive membership for user:', userEmail);
        const { data: reactivatedMembership, error: reactivateError } = await supabase
          .from('project_users')
          .update({
            status: 'invited',
            is_active: true,
            assigned_date: new Date().toISOString().split('T')[0],
            deleted_at: null,
            deleted_by: null,
            updated_at: new Date().toISOString(),
            updated_by: currentUserId,
          })
          .eq('id', existingMembership.id)
          .select()
          .single();

        if (reactivateError) {
          console.error('Error reactivating membership:', reactivateError);
          return NextResponse.json(
            { error: 'Failed to reactivate user membership' },
            { status: 500 },
          );
        }

        invitation = reactivatedMembership;
      }
    } else {
      // No existing membership - create new record
      const { data: newInvitation, error: inviteError } = await supabase
        .from('project_users')
        .insert({
          project_id: projectId,
          user_id: existingUser.id,
          role: role,
          status: 'invited',
          assigned_date: new Date().toISOString().split('T')[0],
          is_active: true,
          created_by: currentUserId,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (inviteError) {
        console.error('Error creating invitation:', inviteError);
        return NextResponse.json(
          { error: 'Failed to create invitation' },
          { status: 500 },
        );
      }

      invitation = newInvitation;
    }



    return NextResponse.json(
      {
        success: true,
        message: 'User invited successfully',
        invitation,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Invite user error:', error);

    // Provide more specific error messages for common issues
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (errorMessage.includes('duplicate key value violates unique constraint')) {
      return NextResponse.json(
        {
          error: 'User invitation conflict',
          message: 'Unable to create invitation due to existing membership. Please try again.'
        },
        { status: 409 },
      );
    }

    if (errorMessage.includes('foreign key constraint')) {
      return NextResponse.json(
        {
          error: 'Invalid reference',
          message: 'Project or user reference is invalid.'
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'An unexpected error occurred while processing the invitation.'
      },
      { status: 500 },
    );
  }
}
