'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    Sheet<PERSON><PERSON><PERSON>,
    SheetTrigger,
} from '@/components/ui/sheet';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { useNavigationTranslations } from '@/hooks/use-translations';
import { Link, usePathname, useRouter } from '@/i18n/navigation';
import { clearProjectIdFromCookies } from '@/lib/middleware-utils';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { useQueryClient } from '@tanstack/react-query';
import {
    BarChart3,
    Calendar,
    ChevronRight,
    Home,
    Menu,
    MessageSquare,
    Settings,
    Shield,
    UserCheck,
    Users as UsersIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';

interface ProjectHeaderProps {
  projectName?: string;
  module: string;
  isLoading?: boolean;
}

// Menu item interface
interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
  translationKey: string;
}

// Project context menu items
const ADMIN_PROJECT_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: BarChart3,
    translationKey: 'dashboard',
  },
  {
    title: 'PMAs',
    url: '/pmas',
    icon: Shield,
    translationKey: 'pmas',
  },
  {
    title: 'Maintenance Logs',
    url: '/maintenance-logs',
    icon: Calendar,
    translationKey: 'maintenanceLogs',
  },
  {
    title: 'Complaints',
    url: '/admincomplaint',
    icon: MessageSquare,
    translationKey: 'complaints',
  },
  {
    title: 'CP List',
    url: '/cp-list',
    icon: UserCheck,
    translationKey: 'cpList',
  },
  {
    title: 'Members',
    url: '/members',
    icon: UsersIcon,
    translationKey: 'members',
  },
];

const CONTRACTOR_PROJECT_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: BarChart3,
    translationKey: 'dashboard',
  },
  {
    title: 'PMAs',
    url: '/pmas',
    icon: Shield,
    translationKey: 'pmas',
  },
  {
    title: 'Maintenance Logs',
    url: '/maintenance-logs',
    icon: Calendar,
    translationKey: 'maintenanceLogs',
  },
  {
    title: 'Complaints',
    url: '/complaints',
    icon: MessageSquare,
    translationKey: 'complaints',
  },
  {
    title: 'Members',
    url: '/members',
    icon: UsersIcon,
    translationKey: 'members',
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: Settings,
    translationKey: 'settings',
  },
];

// Utility function
const getCleanPathname = (pathname: string): string => {
  return pathname.replace(/^\/(en|ms)/, '') || '/';
};

const isRouteActive = (url: string, pathname: string): boolean => {
  const cleanPathname = getCleanPathname(pathname);

  if (url === '/projects') {
    return cleanPathname === '/projects' || cleanPathname === '/';
  }
  if (url === '/dashboard') {
    return cleanPathname === '/dashboard';
  }
  return cleanPathname.startsWith(url);
};

export function ProjectHeader({
  projectName,
  module,
  isLoading,
}: ProjectHeaderProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { clearProject, setSelectedProject, selectedProjectId } =
    useProjectContext();
  const isMobile = useIsMobile();
  const { userRole } = usePermissions();
  const t = useNavigationTranslations();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Determine if we're in a project context and get the appropriate menu items
  const isInProjectContext = Boolean(selectedProjectId);
  const currentMenuItems = isInProjectContext
    ? userRole === 'admin'
      ? ADMIN_PROJECT_MENU_ITEMS
      : CONTRACTOR_PROJECT_MENU_ITEMS
    : [];

  // Handle clearing project when clicking "Projects"
  const handleBackToProjects = useCallback(() => {
    try {
      // Immediately clear cookies to prevent refetch
      clearProjectIdFromCookies();
      // Set a flag to indicate user intentionally cleared project
      sessionStorage.setItem('user_cleared_project', 'true');
      // Immediately update query data to trigger re-render
      queryClient.setQueryData(['selectedProject'], null);
      // Clear project context
      clearProject();
      setSelectedProject(null);
      // Navigate after clearing context
      router.replace('/projects');
    } catch (error) {
      console.error('Failed to navigate back to projects:', error);
    }
  }, [clearProject, setSelectedProject, router, queryClient]);

  return (
    <header className="w-full">
      {/* Minimalist Navigation Bar */}
      <div className="border-b border-gray-200 bg-white">
        <div className="w-full max-w-full px-3 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex items-center justify-between min-w-0">
            {/* Simple Breadcrumbs */}
            <nav
              className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm min-w-0 flex-1 overflow-hidden"
              aria-label="Breadcrumb"
            >
              <Link
                href="/dashboard"
                className="text-gray-500 hover:text-gray-700 flex-shrink-0"
              >
                <Home className="h-3 w-3 sm:h-4 sm:w-4" />
              </Link>
              <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-300 flex-shrink-0" />
              <button
                onClick={handleBackToProjects}
                className="text-gray-600 hover:text-gray-900 flex-shrink-0"
              >
                Projects
              </button>
              {(projectName || isLoading) && (
                <>
                  <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-300 flex-shrink-0" />
                  <span className="text-gray-600 max-w-[120px] sm:max-w-[200px] truncate">
                    {isLoading ? (
                      <span className="animate-pulse bg-gray-300 h-4 w-16 rounded inline-block"></span>
                    ) : (
                      projectName
                    )}
                  </span>
                </>
              )}
              <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-300 flex-shrink-0" />
              <span className="text-gray-900 font-medium truncate">
                {module}
              </span>
            </nav>

            {/* Mobile Navigation Menu - Only show in project context on mobile */}
            {isMobile && isInProjectContext && currentMenuItems.length > 0 && (
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-600 hover:text-gray-900"
                  >
                    <Menu className="h-4 w-4" />
                    <span className="sr-only">Open navigation menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80 sm:w-96">
                  <SheetHeader className="text-left pb-4">
                    <SheetTitle>Project Navigation</SheetTitle>
                    <SheetDescription>
                      Quick access to all project features
                    </SheetDescription>
                  </SheetHeader>
                  <div className="grid gap-2">
                    {currentMenuItems.map((item) => {
                      const isActive = isRouteActive(item.url, pathname);
                      const IconComponent = item.icon;
                      return (
                        <Link
                          key={item.translationKey}
                          href={item.url}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={cn(
                            'flex items-center gap-3 p-3 rounded-lg transition-colors',
                            isActive
                              ? 'bg-primary/10 text-primary border border-primary/20'
                              : 'text-muted-foreground hover:text-foreground hover:bg-muted/50',
                          )}
                        >
                          <IconComponent className="h-5 w-5 flex-shrink-0" />
                          <span className="font-medium">
                            {t(item.translationKey)}
                          </span>
                        </Link>
                      );
                    })}
                  </div>
                </SheetContent>
              </Sheet>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
